import './assets/main.css'

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import App from './App.vue'

// Import routes
import { routes } from './router/index'

// Create router
const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Create pinia store
const pinia = createPinia()

// Create app
const app = createApp(App)
app.use(pinia)
app.use(router)

// Router guards
router.beforeEach(async (to, from, next) => {
  const { useAuthStore } = await import('./stores/auth')
  const authStore = useAuthStore()

  console.log('Router guard - navigating to:', to.path)
  console.log('Router guard - auth loading:', authStore.isLoading)
  console.log('Router guard - auth authenticated:', authStore.isAuthenticated)

  // Wait for auth initialization if still loading
  if (authStore.isLoading) {
    console.log('Router guard - waiting for auth initialization...')
    // Wait a bit for initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100))
    console.log('Router guard - auth initialization wait complete')
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  console.log('Router guard - requires auth:', requiresAuth)

  if (requiresAuth && !authStore.isAuthenticated) {
    console.log('Router guard - redirecting unauthenticated user to login')
    // Redirect to login with return URL
    const returnTo = encodeURIComponent(to.fullPath)
    next(`/?returnTo=${returnTo}`)
  } else if (!requiresAuth && authStore.isAuthenticated && to.path === '/') {
    console.log('Router guard - redirecting authenticated user away from login')
    // Redirect authenticated users away from login
    const returnTo = to.query.returnTo as string
    next(returnTo ? decodeURIComponent(returnTo) : '/layout')
  } else {
    console.log('Router guard - allowing navigation')
    next()
  }
})

// Initialize auth store
const { useAuthStore } = await import('./stores/auth')
const authStore = useAuthStore()
await authStore.initialize()

app.mount('#app')

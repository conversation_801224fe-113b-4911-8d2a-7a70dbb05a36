import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'

interface SpotifyImage {
  url: string
  height: number | null
  width: number | null
}

interface SpotifyUser {
  id: string
  display_name: string
  type: string
}

interface SpotifyArtist {
  id: string
  name: string
  uri: string
}

interface SpotifyAlbum {
  id: string
  name: string
  images: SpotifyImage[]
}

interface SpotifyTrack {
  id: string
  name: string
  uri: string
  duration_ms: number
  artists: SpotifyArtist[]
  album: SpotifyAlbum
}

interface Playlist {
  id: string
  name: string
  description?: string
  images: SpotifyImage[]
  owner: SpotifyUser
  public: boolean
  collaborative: boolean
  tracks?: SpotifyTrack[]
  uri: string
  snapshot_id: string
}

interface LikedSongs {
  items: SpotifyTrack[]
  total: number
  limit: number
  offset: number
}

export const usePlaylistsStore = defineStore('playlists', () => {
  // State
  const playlists = ref<Playlist[]>([])
  const likedSongs = ref<LikedSongs | null>(null)
  const selectedPlaylist = ref<Playlist | null>(null)
  const isLoadingPlaylists = ref(false)
  const isLoadingLikedSongs = ref(false)
  const isShuffling = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const hasPlaylists = computed(() => playlists.value.length > 0)
  const hasLikedSongs = computed(() => likedSongs.value && likedSongs.value.total > 0)

  // Actions
  const fetchPlaylists = async () => {
    const authStore = useAuthStore()
    console.log('Fetching playlists... Token exists:', !!authStore.token)
    if (!authStore.token) return

    try {
      isLoadingPlaylists.value = true
      error.value = null

      const response = await fetch(`${BACKEND_URL}/api/playlists`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      })

      console.log('Playlists API response status:', response.status)

      if (!response.ok) {
        throw new Error('Failed to fetch playlists')
      }

      const data = await response.json()
      playlists.value = data.items || []
      console.log('Playlists fetched:', playlists.value.length)
    } catch (err) {
      console.error('Error fetching playlists:', err)
      error.value = 'Failed to load playlists'
    } finally {
      isLoadingPlaylists.value = false
    }
  }

  const fetchLikedSongs = async () => {
    const authStore = useAuthStore()
    console.log('Fetching liked songs... Token exists:', !!authStore.token)
    if (!authStore.token) return

    try {
      isLoadingLikedSongs.value = true
      error.value = null

      const response = await fetch(`${BACKEND_URL}/api/liked-songs?limit=50`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      })

      console.log('Liked songs API response status:', response.status)

      if (!response.ok) {
        throw new Error('Failed to fetch liked songs')
      }

      const data = await response.json()
      likedSongs.value = data
      console.log('Liked songs fetched:', data?.items?.length || 0)
    } catch (err) {
      console.error('Error fetching liked songs:', err)
      error.value = 'Failed to load liked songs'
    } finally {
      isLoadingLikedSongs.value = false
    }
  }

  const selectPlaylist = (playlist: Playlist) => {
    selectedPlaylist.value = playlist
  }

  const shufflePlaylist = async (playlistId: string) => {
    const authStore = useAuthStore()
    if (!authStore.token) return null

    try {
      isShuffling.value = true
      error.value = null

      const endpoint = playlistId === 'liked-songs' 
        ? `${BACKEND_URL}/api/liked-songs/shuffle`
        : `${BACKEND_URL}/api/playlists/${playlistId}/shuffle`

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to shuffle playlist')
      }

      const shuffledPlaylist = await response.json()
      return shuffledPlaylist
    } catch (err) {
      console.error('Error shuffling playlist:', err)
      error.value = 'Failed to shuffle playlist'
      return null
    } finally {
      isShuffling.value = false
    }
  }

  const initialize = async () => {
    console.log('Playlists store initializing...')
    await Promise.all([
      fetchPlaylists(),
      fetchLikedSongs()
    ])
    console.log('Playlists store initialization complete. Playlists:', playlists.value.length, 'Liked songs:', likedSongs.value?.items?.length || 0)
  }

  return {
    // State
    playlists,
    likedSongs,
    selectedPlaylist,
    isLoadingPlaylists,
    isLoadingLikedSongs,
    isShuffling,
    error,
    // Computed
    hasPlaylists,
    hasLikedSongs,
    // Actions
    fetchPlaylists,
    fetchLikedSongs,
    selectPlaylist,
    shufflePlaylist,
    initialize
  }
})

version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    volumes:
      - ./backend:/app
    environment:
      - SPOTIFY_CLIENT_ID=${SPOTIFY_CLIENT_ID}
      - SPOTIFY_CLIENT_SECRET=${SPOTIFY_CLIENT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:5174}
      - BACKEND_URL=${BACKEND_URL:-http://localhost:8080}
      - SPOTIFY_REDIRECT_URI=${SPOTIFY_REDIRECT_URI:-http://localhost:5174/callback}
      - SPOTIFY_SCOPES=${SPOTIFY_SCOPES:-user-library-read playlist-read-private playlist-modify-public playlist-modify-private user-top-read playlist-read-collaborative}
    networks:
      - spotify-network

networks:
  spotify-network:
    driver: bridge 